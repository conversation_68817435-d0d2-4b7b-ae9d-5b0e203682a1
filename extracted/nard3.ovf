<?xml version="1.0" encoding="UTF-8"?>
<!--Generated by VMware ovftool 4.6.3 (build-24031167), UTC time: 2025-03-04T22:15:12.361481Z-->
<Envelope vmw:buildId="build-24031167" xmlns="http://schemas.dmtf.org/ovf/envelope/1" xmlns:cim="http://schemas.dmtf.org/wbem/wscim/1/common" xmlns:ovf="http://schemas.dmtf.org/ovf/envelope/1" xmlns:rasd="http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/CIM_ResourceAllocationSettingData" xmlns:vmw="http://www.vmware.com/schema/ovf" xmlns:vssd="http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/CIM_VirtualSystemSettingData" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <References>
    <File ovf:href="nard3-file1.iso" ovf:id="file1" ovf:size="4526702592"/>
    <File ovf:href="nard3-disk1.vmdk" ovf:id="file2" ovf:size="2933723136"/>
  </References>
  <DiskSection>
    <Info>Virtual disk information</Info>
    <Disk ovf:capacity="60" ovf:capacityAllocationUnits="byte * 2^30" ovf:diskId="vmdisk1" ovf:fileRef="file2" ovf:format="http://www.vmware.com/interfaces/specifications/vmdk.html#streamOptimized" ovf:populatedSize="7868579840"/>
  </DiskSection>
  <NetworkSection>
    <Info>The list of logical networks</Info>
    <Network ovf:name="nat">
      <Description>The nat network</Description>
    </Network>
  </NetworkSection>
  <VirtualSystem ovf:id="vm">
    <Info>A virtual machine</Info>
    <Name>nard3</Name>
    <OperatingSystemSection ovf:id="100" vmw:osType="fedora64Guest">
      <Info>The kind of installed guest operating system</Info>
    </OperatingSystemSection>
    <VirtualHardwareSection>
      <Info>Virtual hardware requirements</Info>
      <System>
        <vssd:ElementName>Virtual Hardware Family</vssd:ElementName>
        <vssd:InstanceID>0</vssd:InstanceID>
        <vssd:VirtualSystemIdentifier>nard3</vssd:VirtualSystemIdentifier>
        <vssd:VirtualSystemType>vmx-18</vssd:VirtualSystemType>
      </System>
      <Item>
        <rasd:AllocationUnits>hertz * 10^6</rasd:AllocationUnits>
        <rasd:Description>Number of Virtual CPUs</rasd:Description>
        <rasd:ElementName>4 virtual CPU(s)</rasd:ElementName>
        <rasd:InstanceID>1</rasd:InstanceID>
        <rasd:ResourceType>3</rasd:ResourceType>
        <rasd:VirtualQuantity>4</rasd:VirtualQuantity>
        <vmw:CoresPerSocket ovf:required="false">1</vmw:CoresPerSocket>
      </Item>
      <Item>
        <rasd:AllocationUnits>byte * 2^20</rasd:AllocationUnits>
        <rasd:Description>Memory Size</rasd:Description>
        <rasd:ElementName>15360MB of memory</rasd:ElementName>
        <rasd:InstanceID>2</rasd:InstanceID>
        <rasd:ResourceType>4</rasd:ResourceType>
        <rasd:VirtualQuantity>15360</rasd:VirtualQuantity>
      </Item>
      <Item ovf:required="false">
        <rasd:Address>0</rasd:Address>
        <rasd:Description>USB Controller (EHCI)</rasd:Description>
        <rasd:ElementName>usb</rasd:ElementName>
        <rasd:InstanceID>3</rasd:InstanceID>
        <rasd:ResourceSubType>vmware.usb.ehci</rasd:ResourceSubType>
        <rasd:ResourceType>23</rasd:ResourceType>
        <vmw:Config ovf:required="false" vmw:key="ehciEnabled" vmw:value="true"/>
      </Item>
      <Item>
        <rasd:Address>0</rasd:Address>
        <rasd:Description>SCSI Controller</rasd:Description>
        <rasd:ElementName>scsiController0</rasd:ElementName>
        <rasd:InstanceID>4</rasd:InstanceID>
        <rasd:ResourceSubType>lsilogic</rasd:ResourceSubType>
        <rasd:ResourceType>6</rasd:ResourceType>
      </Item>
      <Item>
        <rasd:Address>1</rasd:Address>
        <rasd:Description>IDE Controller</rasd:Description>
        <rasd:ElementName>ideController1</rasd:ElementName>
        <rasd:InstanceID>5</rasd:InstanceID>
        <rasd:ResourceType>5</rasd:ResourceType>
      </Item>
      <Item>
        <rasd:AddressOnParent>0</rasd:AddressOnParent>
        <rasd:AutomaticAllocation>true</rasd:AutomaticAllocation>
        <rasd:ElementName>cdrom0</rasd:ElementName>
        <rasd:HostResource>ovf:/file/file1</rasd:HostResource>
        <rasd:InstanceID>6</rasd:InstanceID>
        <rasd:Parent>5</rasd:Parent>
        <rasd:ResourceType>15</rasd:ResourceType>
        <vmw:Config ovf:required="false" vmw:key="connectable.allowGuestControl" vmw:value="false"/>
      </Item>
      <Item>
        <rasd:AddressOnParent>0</rasd:AddressOnParent>
        <rasd:ElementName>disk0</rasd:ElementName>
        <rasd:HostResource>ovf:/disk/vmdisk1</rasd:HostResource>
        <rasd:InstanceID>7</rasd:InstanceID>
        <rasd:Parent>4</rasd:Parent>
        <rasd:ResourceType>17</rasd:ResourceType>
        <vmw:Config ovf:required="false" vmw:key="connectable.allowGuestControl" vmw:value="false"/>
      </Item>
      <Item>
        <rasd:AddressOnParent>2</rasd:AddressOnParent>
        <rasd:AutomaticAllocation>true</rasd:AutomaticAllocation>
        <rasd:Connection>nat</rasd:Connection>
        <rasd:Description>VmxNet3 ethernet adapter on &quot;nat&quot;</rasd:Description>
        <rasd:ElementName>ethernet0</rasd:ElementName>
        <rasd:InstanceID>8</rasd:InstanceID>
        <rasd:ResourceSubType>VmxNet3</rasd:ResourceSubType>
        <rasd:ResourceType>10</rasd:ResourceType>
        <vmw:Config ovf:required="false" vmw:key="slotInfo.pciSlotNumber" vmw:value="160"/>
        <vmw:Config ovf:required="false" vmw:key="connectable.allowGuestControl" vmw:value="false"/>
      </Item>
      <Item ovf:required="false">
        <rasd:AutomaticAllocation>false</rasd:AutomaticAllocation>
        <rasd:ElementName>video</rasd:ElementName>
        <rasd:InstanceID>9</rasd:InstanceID>
        <rasd:ResourceType>24</rasd:ResourceType>
        <vmw:Config ovf:required="false" vmw:key="videoRamSizeInKB" vmw:value="262144"/>
        <vmw:Config ovf:required="false" vmw:key="enable3DSupport" vmw:value="true"/>
      </Item>
      <Item ovf:required="false">
        <rasd:AutomaticAllocation>false</rasd:AutomaticAllocation>
        <rasd:ElementName>vmci</rasd:ElementName>
        <rasd:InstanceID>10</rasd:InstanceID>
        <rasd:ResourceSubType>vmware.vmci</rasd:ResourceSubType>
        <rasd:ResourceType>1</rasd:ResourceType>
      </Item>
      <vmw:Config ovf:required="false" vmw:key="cpuHotAddEnabled" vmw:value="true"/>
      <vmw:Config ovf:required="false" vmw:key="memoryHotAddEnabled" vmw:value="true"/>
      <vmw:Config ovf:required="false" vmw:key="simultaneousThreads" vmw:value="1"/>
      <vmw:Config ovf:required="false" vmw:key="virtualNuma.coresPerNumaNode" vmw:value="0"/>
      <vmw:Config ovf:required="false" vmw:key="tools.syncTimeWithHost" vmw:value="false"/>
      <vmw:Config ovf:required="false" vmw:key="tools.afterPowerOn" vmw:value="true"/>
      <vmw:Config ovf:required="false" vmw:key="tools.afterResume" vmw:value="true"/>
      <vmw:Config ovf:required="false" vmw:key="tools.beforeGuestShutdown" vmw:value="true"/>
      <vmw:Config ovf:required="false" vmw:key="tools.beforeGuestStandby" vmw:value="true"/>
      <vmw:Config ovf:required="false" vmw:key="powerOpInfo.powerOffType" vmw:value="soft"/>
      <vmw:Config ovf:required="false" vmw:key="powerOpInfo.resetType" vmw:value="soft"/>
      <vmw:Config ovf:required="false" vmw:key="powerOpInfo.suspendType" vmw:value="soft"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="cpuid.coresPerSocket" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="ehci.pciSlotNumber" vmw:value="35"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="ethernet0.pciSlotNumber" vmw:value="160"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="guestInfo.detailed.data" vmw:value="bitness=&apos;64&apos; distroName=&apos;Red Hat Enterprise Linux Server&apos; distroVersion=&apos;7.9&apos; familyName=&apos;Linux&apos; kernelVersion=&apos;3.10.0-1160.15.2.el7.x86_64&apos; prettyName=&apos;Red Hat Enterprise Linux Server 7.9 (Maipo)&apos;"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="guestOS.detailed.data" vmw:value="bitness=&apos;64&apos; distroName=&apos;Red Hat Enterprise Linux Server&apos; distroVersion=&apos;7.9&apos; familyName=&apos;Linux&apos; kernelVersion=&apos;3.10.0-1160.15.2.el7.x86_64&apos; prettyName=&apos;Red Hat Enterprise Linux Server 7.9 (Maipo)&apos;"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="hpet0.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="ide1:0.autodetect" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="isolation.tools.hgfs.disable" vmw:value="TRUE"/>
      
      <vmw:ExtraConfig ovf:required="false" vmw:key="monitor.phys_bits_used" vmw:value="45"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="nvram" vmw:value="nard3-file2.nvram"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge0.pciSlotNumber" vmw:value="17"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge0.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge4.functions" vmw:value="8"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge4.pciSlotNumber" vmw:value="21"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge4.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge4.virtualDev" vmw:value="pcieRootPort"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge5.functions" vmw:value="8"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge5.pciSlotNumber" vmw:value="22"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge5.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge5.virtualDev" vmw:value="pcieRootPort"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge6.functions" vmw:value="8"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge6.pciSlotNumber" vmw:value="23"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge6.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge6.virtualDev" vmw:value="pcieRootPort"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge7.functions" vmw:value="8"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge7.pciSlotNumber" vmw:value="24"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge7.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="pciBridge7.virtualDev" vmw:value="pcieRootPort"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="scsi0.pciSlotNumber" vmw:value="16"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder.maxNum" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.enabled" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.expiration" vmw:value="never"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.guestName" vmw:value="Shared-Virtual-Machines"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.hostPath" vmw:value="C:\Users\<USER>\Shared-Virtual-Machines"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.readAccess" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sharedFolder0.writeAccess" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="softPowerOff" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="sound.pciSlotNumber" vmw:value="-1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="svga.guestBackedPrimaryAware" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="tools.remindInstall" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb.pciSlotNumber" vmw:value="32"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb.vbluetooth.startConnected" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:0.deviceType" vmw:value="hid"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:0.parent" vmw:value="-1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:0.port" vmw:value="0"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:0.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:1.deviceType" vmw:value="hub"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:1.parent" vmw:value="-1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:1.port" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:1.present" vmw:value="TRUE"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="usb:1.speed" vmw:value="2"/>
      
      <vmw:ExtraConfig ovf:required="false" vmw:key="virtualHW.productCompatibility" vmw:value="hosted"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmci0.pciSlotNumber" vmw:value="36"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmci0.unrestricted" vmw:value="false"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.checkpointSVGAPrimarySize" vmw:value="268435456"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.baseCapsLevel" vmw:value="9"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.bc67" vmw:value="9"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.dxMaxConstantBuffers" vmw:value="15"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.dxProvokingVertex" vmw:value="0"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.graphicsMemoryKB" vmw:value="262144"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.lineStipple" vmw:value="0"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.logicBlendOps" vmw:value="0"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.logicOps" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.maxPointSize" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.maxTextureAnisotropy" vmw:value="16"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.maxTextureSize" vmw:value="16384"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.maxVolumeExtent" vmw:value="2048"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.mobMaxSize" vmw:value="1073741824"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.msFullQuality" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.multisample2x" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.multisample4x" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.multisample8x" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.sm41" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.sm5" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmotion.svga.supports3D" vmw:value="1"/>
      <vmw:ExtraConfig ovf:required="false" vmw:key="vmxstats.filename" vmw:value="nard3.scoreboard"/>
    </VirtualHardwareSection>
  </VirtualSystem>
</Envelope>
                            